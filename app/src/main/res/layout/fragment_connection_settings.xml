<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    style="@style/base_layout_style"
    tools:context=".rfidreader.settings.ApplicationSettingsFragment">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

    <TextView
        android:id="@+id/readerSettingsTitle"
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="bottom"
         android:text="@string/reader_conn_sett" />

    <include layout="@layout/title_divider" />

 <!--<TableRow
 android:id="@+id/autoDetectReadersRow"
 android:layout_width="match_parent"
 android:layout_height="0dp"
 android:layout_weight="10"
 android:gravity="center">

 <TextView
     android:id="@+id/autoDetectReadersTitle"
     style="@style/style_normal_font"
     android:layout_width="0dp"
     android:layout_weight="85"
     android:gravity="left"
     android:text="@string/auto_detect_readers" />

 <CheckBox
     android:id="@+id/autoDetectReaders"
     android:layout_width="0dp"
     android:layout_height="wrap_content"
     android:layout_weight="15"
     android:gravity="left" />
</TableRow>

<include layout="@layout/normal_divider"/>-->

    <TableRow
        android:id="@+id/autoReconnectReadersRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            android:id="@+id/autoReconnectReadersTitle"
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:checked="true"
            android:gravity="left"
            android:text="@string/auto_reconnect_reader" />

        <CheckBox
            android:id="@+id/autoReconnectReaders"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="15"
            android:gravity="left" />
    </TableRow>

    <TextView
        android:id="@+id/notificationSettingsTitle"
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="bottom"
        android:text="@string/notification_settings" />

    <include layout="@layout/normal_divider" />

    <TableRow
        android:id="@+id/readerConnectionRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            android:id="@+id/readerConnectionTitle"
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:gravity="left"
            android:text="@string/readers_connection" />

        <CheckBox
            android:id="@+id/readerConnection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="left" />
    </TableRow>

    <include layout="@layout/normal_divider" />

    <TableRow
        android:id="@+id/readerBatteryRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            android:id="@+id/readerBatteryTitle"
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:gravity="left"
            android:text="@string/reader_battery_status" />

        <CheckBox
            android:id="@+id/readerBattery"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="left" />
    </TableRow>



    <TextView
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="bottom"
        android:text="@string/export_data_sett" />

    <include layout="@layout/title_divider" />

    <TableRow
        android:id="@+id/autoDetectReadersRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:gravity="left"
            android:text="@string/export_data" />

        <CheckBox
            android:id="@+id/exportData"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="left" />
    </TableRow>

  <!--  <TextView
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="bottom"
        android:text="@string/KeyMapping" />

    <include layout="@layout/title_divider" />

    <TableRow
        android:id="@+id/Key_Mapping"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            android:id="@+id/keymaptextview"
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:gravity="left"
            android:text="Select TriggerMap" />

        <Spinner
            android:id="@+id/KeyMapSelect"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:prompt="@string/KeyMappingSelect"
         />

 </TableRow>
-->
 <TextView
     style="@style/style_medium_font"
     android:layout_width="wrap_content"
     android:layout_height="wrap_content"
     android:layout_marginTop="5dp"
     android:gravity="bottom"
     android:text="@string/match_mode" />

 <include layout="@layout/title_divider" />

 <TableRow
     android:id="@+id/tagListMatchModeRow"
     android:layout_width="match_parent"
     android:layout_height="wrap_content"
     android:gravity="center">

     <TextView
         style="@style/style_normal_font"
         android:layout_width="0dp"
         android:layout_weight="85"
         android:gravity="left"
         android:text="@string/tag_list_match_mode" />

     <CheckBox
         android:id="@+id/tagListMatchMode"
         android:layout_width="0dp"
         android:layout_height="wrap_content"
         android:layout_weight="15"
         android:gravity="left" />
 </TableRow>

 <include layout="@layout/normal_divider" />

 <TableRow
     android:id="@+id/tagListMatchTagNamesTitle"
     android:layout_width="match_parent"
     android:layout_height="wrap_content"
     android:gravity="center">

     <TextView
         style="@style/style_normal_font"
         android:layout_width="0dp"
         android:layout_weight="85"
         android:gravity="left"
         android:text="@string/tag_list_match_tag_names" />

     <CheckBox
         android:id="@+id/tagListMatchTagNames"
         android:layout_width="0dp"
         android:layout_height="wrap_content"
         android:layout_weight="15"
         android:checked="true"
         android:gravity="left" />
 </TableRow>

 <include layout="@layout/normal_divider" />
 

 <TableRow
     android:id="@+id/AsciiModeTitle"
     android:layout_width="match_parent"
     android:layout_height="wrap_content"
     android:gravity="center">

     <TextView
         style="@style/style_normal_font"
         android:layout_width="0dp"
         android:layout_weight="85"
         android:gravity="left"
         android:text="@string/ascii_mode" />

     <CheckBox
         android:id="@+id/asciiMode"
         android:layout_width="0dp"
         android:layout_height="wrap_content"
         android:layout_weight="15"
         android:gravity="left" />
 </TableRow>

            <TableRow
                android:id="@+id/DecodeTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center">

                <TextView
                    style="@style/style_normal_font"
                    android:layout_width="0dp"
                    android:layout_weight="85"
                    android:gravity="left"
                    android:text="@string/sgtin_mode" />

                <CheckBox
                    android:id="@+id/sgtinMode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="15"
                    android:gravity="left" />
            </TableRow>

            
    <TextView
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="bottom"
        android:text="@string/language_setting" />

    <include layout="@layout/title_divider" />

    <TableRow
        android:id="@+id/languageSettingRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="40"
            android:gravity="left"
            android:text="@string/language_setting" />

        <Spinner
            android:id="@+id/spinnerLanguage"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="60"
            android:gravity="right|center_vertical"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:layout_marginRight="4dp" />
    </TableRow>

    <!-- 语言设置验证按钮 -->
    <TableRow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="40"
            android:gravity="left"
            android:text="@string/language_validation" />

        <Button
            android:id="@+id/btnValidateLanguage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="60"
            android:text="@string/validate_language_settings"
            android:textSize="12sp"
            android:padding="8dp"
            android:layout_marginRight="4dp" />
    </TableRow>

    <include layout="@layout/normal_divider" />
<!-- Remove android:visibility="invisible" in below textview,include ,tablerow
for Bluetooth Configuration in Application Settings  -->
    <TextView
        style="@style/style_medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="bottom"
        android:text="@string/bluetooth_config"
        android:visibility="invisible"/>

    <include layout="@layout/title_divider"
        android:visibility="invisible"/>

    <TableRow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:visibility="invisible">

        <TextView
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="85"
            android:gravity="left"
            android:text="@string/bluetooth_mode" />
        <TextView
            android:id="@+id/BluetoothmodeText"
            style="@style/style_normal_font"
            android:layout_width="0dp"
            android:layout_weight="55"
            android:gravity="left" />
        <Spinner
            android:id="@+id/spinnerbluetoothmode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="left" />

    </TableRow>

     </LinearLayout>

 </ScrollView>

</LinearLayout>



