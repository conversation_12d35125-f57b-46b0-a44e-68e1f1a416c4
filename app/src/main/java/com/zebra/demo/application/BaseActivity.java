package com.zebra.demo.application;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;

/**
 * 基础Activity类
 * 支持语言切换功能
 */
public abstract class BaseActivity extends AppCompatActivity {

    @Override
    protected void attachBaseContext(Context newBase) {
        try {
            // 获取保存的语言设置
            String savedLanguage = LocaleHelper.getLanguage(newBase);
            Log.d("BaseActivity", "Applying language in attachBaseContext: " + savedLanguage);

            // 在Activity创建时应用语言设置，不重复保存
            Context context = LocaleHelper.setLocale(newBase, savedLanguage, false);
            super.attachBaseContext(context);

            Log.d("BaseActivity", "Language applied successfully in attachBaseContext");
        } catch (Exception e) {
            Log.e("BaseActivity", "Error applying language in attachBaseContext: " + e.getMessage());
            // 发生错误时使用原始context
            super.attachBaseContext(newBase);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 处理配置变化，不重复保存语言设置
        LocaleHelper.setLocale(this, LocaleHelper.getLanguage(this), false);
    }
}