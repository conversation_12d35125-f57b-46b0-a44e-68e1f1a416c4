package com.zebra.demo.rfidreader.rapidread;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.*;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;

import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.zebra.demo.ActiveDeviceActivity;
import com.zebra.demo.R;
import com.zebra.demo.application.Application;
import com.zebra.demo.mes_settings.MESCommunication;
import com.zebra.demo.rfidreader.common.Constants;
import com.zebra.demo.rfidreader.common.ResponseHandlerInterfaces;
import com.zebra.demo.rfidreader.inventory.InventoryListItem;
import com.zebra.demo.rfidreader.reader_connection.ScanPair;
import com.zebra.demo.rfidreader.rfid.RFIDController;
import com.zebra.demo.rfidreader.settings.ISettingsUtil;
import com.zebra.rfid.api3.InvalidUsageException;
import com.zebra.rfid.api3.OperationFailureException;
import com.zebra.rfid.api3.RFIDResults;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.TimeUnit;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.zebra.demo.application.Application.TAG_LIST_LOADED;
import static com.zebra.demo.application.Application.currentFragment;
import static com.zebra.demo.rfidreader.rfid.RFIDController.ActiveProfile;
import static com.zebra.demo.rfidreader.rfid.RFIDController.mConnectedReader;
import static com.zebra.demo.rfidreader.rfid.RFIDController.mIsInventoryRunning;
import static com.zebra.demo.scanner.helpers.ActiveDeviceAdapter.INVENTORY_TAB;
import static com.zebra.demo.scanner.helpers.ActiveDeviceAdapter.RFID_SETTINGS_TAB;
import static com.zebra.demo.scanner.helpers.ActiveDeviceAdapter.RFID_TAB;
import static com.zebra.demo.scanner.helpers.ActiveDeviceAdapter.SETTINGS_TAB;

/**
 * A simple {@link android.support.v4.app Fragment } subclass.
 * <p/>
 * Use the {@link RapidReadFragment#newInstance} factory method to
 * create a new instance of this fragment.
 * <p/>
 * Fragment to handle the rapid read operation and UI
 */
public class RapidReadFragment extends Fragment
        implements ResponseHandlerInterfaces.ResponseTagHandler, ResponseHandlerInterfaces.TriggerEventHandler,
        ResponseHandlerInterfaces.BatchModeEventHandler, ResponseHandlerInterfaces.ResponseStatusHandler {
    private static RapidReadFragment mRapidReadFragment = null;
    private static final String TAG = "RapidReadFragment";
    private static final int MAX_SCAN_TIME = 10000; // 最大扫描时间10秒
    private static final int SCAN_CHECK_INTERVAL = 1000; // 检查间隔1秒
    private static final int MAX_TAGS = 1000; // 最大标签数量限制

    // 线程安全的标志位
    private final AtomicBoolean isScanning = new AtomicBoolean(false);
    private final AtomicBoolean isFragmentActive = new AtomicBoolean(false);

    private long scanStartTime = 0;
    private Handler scanHandler;
    private Set<String> scannedTags = new HashSet<>(); // 用于去重的标签集合

    // UI组件
    private MatchModeProgressView progressView;
    private TextView tagReadRate;
    private TextView uniqueTags;
    private TextView totalTags;
    private ExtendedFloatingActionButton inventoryButton;
    private TextView timeText;
    private TextView uniqueTagTitle;
    private TextView totalTagTitle;
    private TextView resultContent;
    private TextView resultTagContentValue;
    private LinearLayout invtoryData;
    private FrameLayout batchModeRR;
    private boolean batchModeEventReceived = false;
    private ISettingsUtil settingsUtil;
    private EditText scanCode, barcodeInput;
    private MESCommunication mesCommunication;

    // 添加防重复处理机制
    private String lastProcessedResult = "";
    private long lastProcessTime = 0;
    private static final long PROCESS_INTERVAL = 1000; // 1秒内不重复处理相同结果

    public RapidReadFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @return A new instance of fragment RapidReadFragment.
     */
    public static RapidReadFragment newInstance() {
        // if( mRapidReadFragment == null )
        mRapidReadFragment = new RapidReadFragment();
        return mRapidReadFragment;
    }

    public static Fragment newInstance(int position) {
        RapidReadFragment fragment = new RapidReadFragment();
        Bundle args = new Bundle();
        // args.putInt(ARG_COUNT, counter);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);

        // 初始化Handler，确保在主线程
        scanHandler = new Handler(Looper.getMainLooper());

        IntentFilter filter = new IntentFilter();
        filter.addAction(getResources().getString(R.string.dw_action));
        filter.addCategory(getResources().getString(R.string.dw_category));
        // getActivity().registerReceiver(scanResultBroadcast, filter);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_rr, container, false);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.menu_rr, menu);
        menu.findItem(R.id.action_inventory).setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                Activity activity = getActivity();
                if (activity instanceof ActiveDeviceActivity) {
                    ((ActiveDeviceActivity) activity).loadNextFragment(INVENTORY_TAB);
                }
                return true;
            }
        });

        menu.findItem(R.id.action_settings).setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                if (RFIDController.mConnectedReader == null) {
                    Context context = getContext();
                    if (context != null) {
                        Toast.makeText(context, "Reader not connected", Toast.LENGTH_SHORT).show();
                    }
                    return true;
                }

                Activity activity = getActivity();
                if (activity instanceof ActiveDeviceActivity) {
                    ((ActiveDeviceActivity) activity).setCurrentTabFocus(SETTINGS_TAB, RFID_SETTINGS_TAB);
                }
                return true;
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        Activity activity = getActivity();
        if (activity == null) {
            Log.e(TAG, "Activity is null in onActivityCreated");
            return;
        }

        if (!(activity instanceof ActiveDeviceActivity)) {
            Log.e(TAG, "Activity is not ActiveDeviceActivity");
            return;
        }

        ActiveDeviceActivity mainActivity = (ActiveDeviceActivity) activity;

        // 安全地获取ActionBar
        ActionBar actionBar = mainActivity.getSupportActionBar();
        if (actionBar != null) {
            actionBar.setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
        }

        // 初始化UI组件，添加空值检查
        try {
            inventoryButton = mainActivity.findViewById(R.id.rr_inventoryButton);
            uniqueTags = mainActivity.findViewById(R.id.uniqueTagContent);
            uniqueTagTitle = mainActivity.findViewById(R.id.uniqueTagTitle);
            totalTags = mainActivity.findViewById(R.id.totalTagContent);
            totalTagTitle = mainActivity.findViewById(R.id.totalTagTitle);
            tagReadRate = activity.findViewById(R.id.readRateContent);
            batchModeRR = activity.findViewById(R.id.batchModeRR);
            invtoryData = activity.findViewById(R.id.inventoryDataLayout);
            resultContent = mainActivity.findViewById(R.id.resultTagContent);
            resultTagContentValue = mainActivity.findViewById(R.id.resultTagContentValue);
            barcodeInput = mainActivity.findViewById(R.id.et_barcode_input);
        } catch (Exception e) {
            Log.e(TAG, "Error initializing UI components: " + e.getMessage());
            return;
        }

        // 设置输入框属性，防止弹出系统输入法
        if (barcodeInput != null) {
            barcodeInput.setShowSoftInputOnFocus(false);
            barcodeInput.setCursorVisible(false);
            barcodeInput.setInputType(android.text.InputType.TYPE_NULL);
            // 设置输入框焦点
            barcodeInput.requestFocus();
        }

        // 启动扫码功能
        if (Application.scanPair == null) {
            Application.scanPair = new ScanPair();
        }
        // 初始化扫码功能
        Application.scanPair.Init(activity, RapidReadFragment.this);

        // 添加文本变化监听器，用于处理扫码结果
        if (barcodeInput != null) {
            barcodeInput.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    // 不需要处理
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    // 不需要处理
                }

                @Override
                public void afterTextChanged(Editable s) {
                    // 当文本变化后，检查是否有有效的扫码结果
                    String scanResult = s.toString().trim();
                    if (!scanResult.isEmpty()) {
                        Log.d(TAG, "检测到扫码结果: " + scanResult);
                        safeUpdateUI(() -> {
                            if (resultContent != null) {
                                resultContent.setText(scanResult);
                            }
                            if (resultTagContentValue != null) {
                                resultTagContentValue.setText(scanResult);
                            }
                        });

                        // 延迟处理，避免在输入过程中触发
                        scanHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                // 检查Fragment是否仍然活跃
                                if (!isFragmentActive.get()) {
                                    return;
                                }

                                // 再次检查文本是否仍然有效（没有被清空）
                                if (barcodeInput != null) {
                                    String currentText = barcodeInput.getText().toString().trim();
                                    if (!currentText.isEmpty() && currentText.equals(scanResult)) {
                                        processCompleted(scanResult);
                                        // 清空输入框，准备下一次扫码
                                        barcodeInput.setText("");
                                    }
                                }
                            }
                        }, 200); // 延迟200ms处理
                    }
                }
            });
        }

        onRapidReadSelected();

        // 读取并显示MES配置信息
        String mesConfig = readMESConfiguration();
        safeUpdateUI(() -> {
            if (resultContent != null) {
                Log.d(TAG, "显示MES配置信息: " + mesConfig);
                resultContent.setText(mesConfig);
            }
        });

        // 初始化MES通信
        initMESCommunication();
    }

    /**
     * 安全地更新UI，确保在主线程中执行
     */
    private void safeUpdateUI(Runnable runnable) {
        Activity activity = getActivity();
        if (activity != null && isFragmentActive.get()) {
            activity.runOnUiThread(runnable);
        }
    }

    /**
     * 初始化MES通信
     */
    private void initMESCommunication() {
        Activity activity = getActivity();
        if (activity == null) {
            Log.e(TAG, "Activity is null, cannot initialize MES communication");
            return;
        }

        try {
            mesCommunication = new MESCommunication(activity);
            mesCommunication.setConnectionCallback(new MESCommunication.ConnectionCallback() {
                @Override
                public void onConnected() {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            Log.d(TAG, "MES服务器连接成功");
                            resultContent.setText(getString(R.string.msg_mes_server_connected));
                        }
                    });
                }

                @Override
                public void onDisconnected() {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            Log.d(TAG, "MES服务器连接断开");
                            resultContent.setText(getString(R.string.msg_mes_server_disconnected));
                        }
                    });
                }

                @Override
                public void onConnectionFailed(String error) {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            Log.d(TAG, "MES服务器连接失败: " + error);
                            resultContent.setText(String.format(getString(R.string.mes_server_connection_failed), error));
                        }
                    });
                }

                @Override
                public void onDataReceived(String data) {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            Log.d(TAG, "收到MES数据: " + data);
                            resultContent.setText(String.format(getString(R.string.received_data), data));
                        }
                    });
                }

                @Override
                public void onConnectionRetrying(int attempt, int maxAttempts, String error) {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            String message = String.format(getString(R.string.connection_failed_retrying), attempt, maxAttempts, error);
                            Log.d(TAG, message);
                            resultContent.setText(message);
                        }
                    });
                }

                @Override
                public void onConnectionRetrySuccess(int attempt) {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            String message = String.format(getString(R.string.reconnection_successful), attempt);
                            Log.d(TAG, message);
                            resultContent.setText(message);
                        }
                    });
                }

                @Override
                public void onConnectionRetryFailed(int maxAttempts, String finalError) {
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            String message = String.format(getString(R.string.connection_failed_after_retries), maxAttempts, finalError);
                            Log.e(TAG, "========= ERROR ============ " + message);
                            resultContent.setText(message);
                        }
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error initializing MES communication: " + e.getMessage());
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isFragmentActive.set(true);

        // 设置输入框焦点和属性
        if (barcodeInput != null) {
            // 设置输入框属性，防止弹出系统输入法
            barcodeInput.setShowSoftInputOnFocus(false);
            barcodeInput.setCursorVisible(false);
            barcodeInput.setInputType(android.text.InputType.TYPE_NULL);
            // 设置输入框焦点
            barcodeInput.requestFocus();
            // 确保输入框为空，准备接收新的扫码结果
            barcodeInput.setText("");
            Log.d(TAG, "onResume: 输入框焦点已设置，准备接收扫码结果");
        }

        // 显示MES配置信息
        if (resultContent != null && !isScanning.get()) {
            String mesConfig = readMESConfiguration();
            Log.d(TAG, "onResume显示MES配置信息: " + mesConfig);
            resultContent.setText(mesConfig);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        isFragmentActive.set(false);

        if (Application.scanPair != null) {
            Application.scanPair.onPause();
        }

        // 停止扫描
        if (isScanning.get()) {
            stopScanning();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isFragmentActive.set(false);

        // 停止扫描
        if (isScanning.get()) {
            stopScanning();
        }

        // 移除所有回调
        if (scanHandler != null) {
            scanHandler.removeCallbacksAndMessages(null);
        }

        // 释放MES通信资源
        if (mesCommunication != null) {
            try {
                // 确保断开连接并释放资源
                mesCommunication.disconnect();
                mesCommunication.release();
            } catch (Exception e) {
                Log.e(TAG, "Error releasing MES communication: " + e.getMessage());
            } finally {
                mesCommunication = null;
            }
        }

        if (Application.scanPair != null) {
            Application.scanPair.onDestroy();
        }
    }

    public void onRapidReadSelected() {
        try {
            Activity activity = getActivity();
            if (activity == null) {
                Log.e(TAG, "Activity is null in onRapidReadSelected");
                return;
            }

            if (RFIDController.mIsInventoryRunning && inventoryButton != null) {
                inventoryButton.setIconResource(R.drawable.ic_play_stop);
            } else if (inventoryButton != null) {
                inventoryButton.setIconResource(android.R.drawable.ic_media_play);
            }

            if (RFIDController.isBatchModeInventoryRunning != null && RFIDController.isBatchModeInventoryRunning) {
                if (invtoryData != null) {
                    invtoryData.setVisibility(View.GONE);
                }
                if (batchModeRR != null) {
                    batchModeRR.setVisibility(View.VISIBLE);
                }
            } else {
                if (invtoryData != null) {
                    invtoryData.setVisibility(View.VISIBLE);
                }
                if (batchModeRR != null) {
                    batchModeRR.setVisibility(View.GONE);
                }
            }

            if (RFIDController.mRRStartedTime == 0) {
                Application.TAG_READ_RATE = 0;
            } else {
                Application.TAG_READ_RATE = (int) (Application.TOTAL_TAGS
                        / (RFIDController.mRRStartedTime / (float) 1000));
            }

            if (tagReadRate != null) {
                tagReadRate.setText(Application.TAG_READ_RATE + Constants.TAGS_SEC);
            }

            timeText = activity.findViewById(R.id.readTimeContent);
            if (timeText != null) {
                String min = String.format("%d", TimeUnit.MILLISECONDS.toMinutes(RFIDController.mRRStartedTime));
                String sec = String.format("%d", TimeUnit.MILLISECONDS.toSeconds(RFIDController.mRRStartedTime) -
                        TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(RFIDController.mRRStartedTime)));
                if (min.length() == 1) {
                    min = "0" + min;
                }
                if (sec.length() == 1) {
                    sec = "0" + sec;
                }
                timeText.setText(min + ":" + sec);
            }

            progressView = activity.findViewById(R.id.MatchModeView);
            if (Application.TAG_LIST_MATCH_MODE && progressView != null) {
                progressView.setVisibility(View.VISIBLE);
            } else if (progressView != null) {
                progressView.setVisibility(View.GONE);
            }

            if (Application.missedTags > 9999 && uniqueTags != null) {
                uniqueTags.setTextSize(45);
            }

            updateTexts();

            View prefilterView = activity.findViewById(R.id.tv_prefilter_enabled);
            if (prefilterView != null) {
                prefilterView.setVisibility(
                        RFIDController.getInstance().isPrefilterEnabled() ? View.VISIBLE : View.INVISIBLE);
            }

            FloatingActionButton bt_clear = activity.findViewById(R.id.bt_clear);
            if (bt_clear != null) {
                bt_clear.setVisibility(ActiveProfile.id.equals("1") ? View.VISIBLE : View.INVISIBLE);
                bt_clear.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (RFIDController.mIsInventoryRunning) {
                            Context context = getContext();
                            if (context != null) {
                                Toast.makeText(context, "Inventory is running", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            try {
                                RFIDController.getInstance().clearAllInventoryData();
                                resetTagsInfo();
                                TAG_LIST_LOADED = false;
                            } catch (Exception e) {
                                Log.e(TAG, "========= ERROR ============ Error clearing inventory: " + e.getMessage());
                                Context context = getContext();
                                if (context != null) {
                                    Toast.makeText(context, "Error clearing inventory", Toast.LENGTH_SHORT).show();
                                }
                            }
                        }
                    }
                });
            }

            settingsUtil = (ActiveDeviceActivity) activity;
            if (Application.TAG_LIST_MATCH_MODE) {
                Log.d("Nikhil1", "onRFIDFragment() RFIDinventoryfragment LoadTagcsv will get call next");
                settingsUtil.LoadTagListCSV();
            }
        } catch (Exception e) {
            Log.e(TAG, "========= ERROR ============ Error in onRapidReadSelected: " + e.getMessage());
            Context context = getContext();
            if (context != null) {
                Toast.makeText(context, "Error occurred", Toast.LENGTH_SHORT).show();
            }
        }
    }

    public void updateTexts() {
        if (Application.TAG_LIST_MATCH_MODE) {
            if (uniqueTags != null && totalTags != null) {
                totalTags.setText(String.valueOf(Application.matchingTags));
                uniqueTags.setText(String.valueOf(Application.missedTags));
            }
            if (totalTagTitle != null && uniqueTagTitle != null) {
                totalTagTitle.setText(R.string.rr_total_tag_title_MM);
                uniqueTagTitle.setText(R.string.rr_unique_tags_title_MM);
            }
            updateProgressView();
        } else {
            if (uniqueTags != null) {
                uniqueTags.setText(String.valueOf(Application.UNIQUE_TAGS));
            }
            if (totalTags != null) {
                totalTags.setText(String.valueOf(Application.TOTAL_TAGS));
            }
            if (totalTagTitle != null && uniqueTagTitle != null) {
                totalTagTitle.setText(R.string.rr_total_tag_title);
                uniqueTagTitle.setText(R.string.rr_unique_tags_title);
            }
        }
    }

    private String readMESConfiguration() {
        try {
            Activity activity = getActivity();
            if (activity == null) {
                return getString(R.string.unable_to_get_activity);
            }

            SharedPreferences sharedPreferences = activity.getSharedPreferences("MESSettings", 0);
            String serverAddress = sharedPreferences.getString("server_address", "");
            String port = sharedPreferences.getString("port", "");
            int connectionType = sharedPreferences.getInt("connection_type", 0);
            int retryCount = sharedPreferences.getInt("retry_count", 3);

            // 显示配置信息
            if (!serverAddress.isEmpty() && !port.isEmpty()) {
                String[] connectionTypes = getResources().getStringArray(R.array.connection_types);
                String message = String.format(getString(R.string.mes_system_config_info),
                        serverAddress, port, connectionTypes[connectionType], retryCount);
                return message;
            }
            return getString(R.string.mes_server_not_configured);
        } catch (Exception e) {
            Log.e(TAG, "Error reading MES configuration: " + e.getMessage());
            return getString(R.string.failed_to_read_mes_config);
        }
    }

    private void updateProgressView() {
        if (progressView == null) {
            return;
        }

        if (Application.missedTags != 0) {
            progressView.mSweepAngle = 360 * Application.matchingTags
                    / (Application.missedTags + Application.matchingTags);
        } else if (Application.matchingTags != 0 && Application.missedTags == 0) {
            progressView.bCompleted = true;
        } else {
            progressView.mSweepAngle = 0;
        }

        if (progressView.mSweepAngle >= 360) {
            progressView.mSweepAngle = 0;
        }

        safeUpdateUI(() -> {
            if (progressView != null) {
                progressView.invalidate();
                progressView.requestLayout();
            }
        });
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    /**
     * method to reset tags info on the screen before starting inventory operation
     */
    public void resetTagsInfo() {
        updateTexts();
        if (progressView != null) {
            progressView.bCompleted = false;
        }
        if (tagReadRate != null) {
            tagReadRate.setText(Application.TAG_READ_RATE + Constants.TAGS_SEC);
        }
        if (timeText != null) {
            timeText.setText(Constants.ZERO_TIME);
        }
    }

    /**
     * method to start inventory operation on trigger press event received
     */
    public void triggerPressEventRecieved() {
        if (!RFIDController.mIsInventoryRunning) {
            safeUpdateUI(() -> {
                Activity activity = getActivity();
                if (activity instanceof ActiveDeviceActivity) {
                    ActiveDeviceActivity activeDeviceActivity = (ActiveDeviceActivity) activity;
                    // 重置扫描状态
                    resetScanState();
                    // 开始扫描
                    activeDeviceActivity.inventoryStartOrStop(null);
                    // 启动扫描时间监控
                    startScanTimeMonitor();
                }
            });
        }
    }

    private void resetScanState() {
        isScanning.set(true);
        scanStartTime = System.currentTimeMillis();
        scannedTags.clear();
        if (Application.tagsReadInventory != null) {
            Application.tagsReadInventory.clear();
        }
        // 清空输入框，准备接收新的扫码结果
        if (barcodeInput != null) {
            barcodeInput.setText("");
            barcodeInput.requestFocus();
            Log.d(TAG, "resetScanState: 输入框已清空，准备接收扫码结果");
        }
    }

    private void startScanTimeMonitor() {
        if (scanHandler == null) {
            return;
        }

        scanHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isScanning.get() || !isFragmentActive.get()) {
                    return;
                }

                long currentTime = System.currentTimeMillis();
                long elapsedTime = currentTime - scanStartTime;

                // 检查是否达到最大扫描时间
                if (elapsedTime >= MAX_SCAN_TIME) {
                    Log.d(TAG, "达到最大扫描时间，停止扫描");
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            resultContent.setText(getString(R.string.max_scan_time_reached));
                        }
                    });
                    stopScanning();
                    return;
                }

                // 检查是否达到最大标签数量
                if (scannedTags.size() >= MAX_TAGS) {
                    Log.d(TAG, "达到最大标签数量限制，停止扫描");
                    safeUpdateUI(() -> {
                        if (resultContent != null) {
                            resultContent.setText(getString(R.string.max_tag_limit_reached));
                        }
                    });
                    stopScanning();
                    return;
                }

                // 继续监控
                if (isScanning.get() && isFragmentActive.get()) {
                    scanHandler.postDelayed(this, SCAN_CHECK_INTERVAL);
                }
            }
        }, SCAN_CHECK_INTERVAL);
    }

    private void stopScanning() {
        safeUpdateUI(() -> {
            Activity activity = getActivity();
            if (activity instanceof ActiveDeviceActivity) {
                ActiveDeviceActivity activeDeviceActivity = (ActiveDeviceActivity) activity;
                if (RFIDController.mIsInventoryRunning) {
                    activeDeviceActivity.inventoryStartOrStop(null);
                    isScanning.set(false);
                    processScanResults();
                }
            }
        });
    }

    private void processScanResults() {
        if (!isFragmentActive.get()) {
            return;
        }

        safeUpdateUI(() -> {
            try {
                // 构建扫描结果
                StringBuilder allTagsInfo = new StringBuilder();
                for (String tag : scannedTags) {
                    allTagsInfo.append(tag).append("\n");
                }

                // 更新UI显示
                if (resultContent != null) {
                    Log.d(TAG, "显示RFID扫描结果: " + allTagsInfo.toString());
                    resultContent.setText(allTagsInfo.toString());
                }
                if (resultTagContentValue != null) {
                    resultTagContentValue.setText(allTagsInfo.toString());
                }

                // 发送RFID数据到MES系统
                if (mesCommunication != null) {
                    // 显示开始发送的状态
                    if (resultContent != null) {
                        Log.d(TAG, "正在连接MES服务器...");
                        resultContent.setText(getString(R.string.connecting_to_mes_server));
                    }

                    // 在后台线程中执行MES发送操作
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Log.d(TAG, "准备发送RFID结果到MES");

                                // 在主线程中更新UI
                                safeUpdateUI(() -> {
                                    if (resultContent != null) {
                                        Log.d(TAG, "准备发送RFID结果到MES");
                                        resultContent.setText(getString(R.string.preparing_to_send_rfid_to_mes) + "\n" + allTagsInfo.toString());
                                    }
                                });

                                // 等待1500ms让用户看到状态
                                Thread.sleep(500);

                                // 执行发送
                                String result = mesCommunication.sendScanResult(allTagsInfo.toString(), "rfid");
                                Log.d(TAG, "MES发送结果: " + result);

                                // 在主线程中更新UI显示结果
                                safeUpdateUI(() -> {
                                    if (resultContent != null) {
                                        Log.d(TAG, "RFID扫码数据已发送");
                                        Log.d(TAG, "RFID发送结果: " + result);
                                        resultContent.setText(String.format(getString(R.string.rfid_send_result), result));

//                                        try {
//                                            JSONObject jsonResult = new JSONObject(result);
//                                            if (jsonResult.has("status")) {
//                                                String status = jsonResult.getString("status");
//                                                if ("success".equalsIgnoreCase(status)) {
//                                                    resultContent.setText(jsonResult.getString("data"));
//                                                } else {
//                                                    resultContent.setText(jsonResult.getString("message"));
//                                                }
//                                            }
//                                        } catch (JSONException e) {
//                                            throw new RuntimeException(e);
//                                        }

//                                        if (result.contains("success")) {
//                                            if (!result.contains("message")) {
//                                                Log.d(TAG, "RFID数据已发送");
//                                                resultContent.setText("RFID数据已发送");
//                                            } else {
//                                                Log.d(TAG, "RFID发送结果: " + result);
//                                                resultContent.setText(result);
//                                            }
//                                        } else {
//                                            Log.d(TAG, "RFID发送失败: " + result);
//                                            resultContent.setText(result);
//                                        }

                                        // 30秒后重新显示MES配置信息
                                        if (scanHandler != null) {
                                            scanHandler.postDelayed(new Runnable() {
                                                @Override
                                                public void run() {
                                                    if (resultContent != null && !isScanning.get()) {
                                                        String mesConfig = readMESConfiguration();
                                                        Log.d(TAG, "30秒后重新显示MES配置信息: " + mesConfig);
                                                        resultContent.setText(mesConfig);
                                                    }
                                                }
                                            }, 300000);
                                    }
                                }

                                 });

                            } catch (Exception e) {
                                Log.e(TAG, "MES发送出错: " + e.getMessage());
                                safeUpdateUI(() -> {
                                    if (resultContent != null) {
                                        Log.e(TAG, "显示MES发送错误: " + e.getMessage());
                                        resultContent.setText(String.format(getString(R.string.mes_send_error), e.getMessage()));
                                    }
                                });
                            }
                        }
                    }).start();

                } else {
                    Log.e(TAG, "mesCommunication 为 null");
                    if (resultContent != null) {
                        Log.e(TAG, "MES通信未初始化");
                        resultContent.setText(getString(R.string.mes_communication_not_initialized));
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "处理扫描结果时出错: " + e.getMessage());
                if (resultContent != null) {
                    Log.e(TAG, "显示处理扫描结果错误: " + e.getMessage());
                    resultContent.setText("处理扫描结果时出错: " + e.getMessage());
                }
            }
        });
    }

    @Override
    public void handleTagResponse(InventoryListItem inventoryListItem, boolean isAddedToList) {
        if (!isFragmentActive.get() || !isScanning.get()) {
            return;
        }

        safeUpdateUI(() -> {
            try {
                // 添加标签到去重集合
                if (inventoryListItem != null && inventoryListItem.getTagID() != null) {
                    scannedTags.add(inventoryListItem.getTagID());
                }

                // 更新UI显示
                updateTexts();
                if (tagReadRate != null) {
                    if (RFIDController.mRRStartedTime == 0) {
                        Application.TAG_READ_RATE = 0;
                    } else {
                        Application.TAG_READ_RATE = (int) (Application.TOTAL_TAGS
                                / (RFIDController.mRRStartedTime / (float) 1000));
                    }
                    tagReadRate.setText(Application.TAG_READ_RATE + Constants.TAGS_SEC);
                }

                // 检查是否达到最大标签数量
                if (scannedTags.size() >= MAX_TAGS) {
                    stopScanning();
                }
            } catch (Exception e) {
                Log.e(TAG, "处理标签响应时出错: " + e.getMessage());
            }
        });
    }

    public void triggerReleaseEventRecieved() {
        if (RFIDController.mIsInventoryRunning) {
            stopScanning();
        }
    }

    public void handleStatusResponse(final RFIDResults results) {
        safeUpdateUI(() -> {
            if (results.equals(RFIDResults.RFID_BATCHMODE_IN_PROGRESS)) {
                if (invtoryData != null) {
                    invtoryData.setVisibility(View.GONE);
                }
                if (batchModeRR != null) {
                    batchModeRR.setVisibility(View.VISIBLE);
                }
            } else if (results.equals(RFIDResults.RFID_OPERATION_IN_PROGRESS)) {
                if (inventoryButton != null) {
                    inventoryButton.setIconResource(R.drawable.ic_play_stop);
                    inventoryButton.setText(R.string.stop);
                }
                mIsInventoryRunning = true;
            } else if (!results.equals(RFIDResults.RFID_API_SUCCESS)) {
                RFIDController.mIsInventoryRunning = false;
                if (inventoryButton != null) {
                    inventoryButton.setIconResource(android.R.drawable.ic_media_play);
                }
                RFIDController.isBatchModeInventoryRunning = false;
            }
        });
    }

    /**
     * method to update inventory details on the screen on operation end summary
     * received
     */
    public void updateInventoryDetails() {
        updateTexts();
        if (tagReadRate != null) {
            tagReadRate.setText(Application.TAG_READ_RATE + Constants.TAGS_SEC);
        }
    }

    /**
     * method to reset inventory operation status on the screen
     */
    public void resetInventoryDetail() {
        safeUpdateUI(() -> {
            if (!ActiveProfile.id.equals("1")) {
                if (inventoryButton != null && !RFIDController.mIsInventoryRunning &&
                        (RFIDController.isBatchModeInventoryRunning == null
                                || !RFIDController.isBatchModeInventoryRunning)) {
                    inventoryButton.setIconResource(android.R.drawable.ic_media_play);
                }
                if (invtoryData != null) {
                    invtoryData.setVisibility(View.VISIBLE);
                }
                if (Application.TAG_LIST_MATCH_MODE && progressView != null) {
                    progressView.setVisibility(View.VISIBLE);
                }

                if (batchModeRR != null) {
                    batchModeRR.setVisibility(View.GONE);
                }

                if (Application.TAG_LIST_MATCH_MODE && Application.matchingTags != 0
                        && Application.missedTags == 0 && progressView != null) {
                    progressView.bCompleted = true;
                }
            }
        });
    }

    @Override
    public void batchModeEventReceived() {
        batchModeEventReceived = true;
        if (inventoryButton != null) {
            inventoryButton.setIconResource(R.drawable.ic_play_stop);
        }
    }

    // 处理扫码结果的方法
    public void processCompleted(String scanResult) {
        if (!isFragmentActive.get() || scanResult == null || scanResult.trim().isEmpty()) {
            return;
        }

        // 防重复处理检查
        long currentTime = System.currentTimeMillis();
        if (scanResult.equals(lastProcessedResult) && (currentTime - lastProcessTime) < PROCESS_INTERVAL) {
            Log.d(TAG, "跳过重复的扫码结果: " + scanResult);
            return;
        }

        // 更新防重复处理状态
        lastProcessedResult = scanResult;
        lastProcessTime = currentTime;

        Log.d(TAG, "开始处理扫码结果: " + scanResult);

        safeUpdateUI(() -> {
            try {
                if (resultContent != null) {
                    Log.d(TAG, "显示扫码结果: " + scanResult);
                    resultContent.setText("扫码结果: " + scanResult);
                    Log.d(TAG, "扫码结果显示在结果区域: " + scanResult);
                }
                if (resultTagContentValue != null) {
                    resultTagContentValue.setText("扫码结果: " + scanResult);
                }

                // 发送数据到MES系统
                if (mesCommunication != null) {
                    // 显示开始发送的状态
                    if (resultContent != null) {
                        Log.d(TAG, "正在连接MES服务器...");
                        resultContent.setText("正在连接MES服务器...");
                    }

                    // 在后台线程中执行MES发送操作
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Log.d(TAG, "准备发送扫码结果到MES");

                                // 在主线程中更新UI
                                safeUpdateUI(() -> {
                                    if (resultContent != null) {
                                        Log.d(TAG, "准备发送扫码结果到MES: " + scanResult);
                                        resultContent.setText("准备发送扫码结果到MES: " + scanResult);
                                    }
                                });

                                // 等待500ms让用户看到状态
                                Thread.sleep(500);

                                // 执行发送
                                String result = mesCommunication.sendScanResult(scanResult, "scan");
                                Log.d(TAG, "SCAN发送结果: " + result);

                                // 在主线程中更新UI显示结果
                                safeUpdateUI(() -> {
//                                    if (resultContent != null) {
//                                        resultContent.setText("SCAN发送结果: " + result);
//                                    }

                                    // 等待500ms后显示最终结果
                                    if (scanHandler != null) {
                                        scanHandler.postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (resultContent != null) {
                                                    Log.d(TAG, "SCAN扫码数据已发送");
                                                    Log.d(TAG, "SCAN发送结果: " + result);
                                                    resultContent.setText("SCAN发送结果\n" + result);

//                                                    try {
//                                                        JSONObject jsonResult = new JSONObject(result);
//                                                        if (jsonResult.has("status")) {
//                                                            String status = jsonResult.getString("status");
//                                                            if ("success".equalsIgnoreCase(status)) {
//                                                                resultContent.setText(jsonResult.getString("data"));
//                                                            } else {
//                                                                resultContent.setText(jsonResult.getString("message"));
//                                                            }
//                                                        }
//                                                    } catch (JSONException e) {
//                                                        throw new RuntimeException(e);
//                                                    }

//                                                    if (result.contains("success")) {
//                                                        Log.d(TAG, "扫码数据已发送");
//
//                                                        if (!result.contains("message")) {
//                                                            Log.d(TAG, "扫码数据已发送");
//                                                            resultContent.setText("扫码数据已发送");
//                                                        } else {
//                                                            Log.d(TAG, "扫码发送结果: " + result);
//                                                            resultContent.setText(result);
//                                                        }
//                                                    } else {
//                                                        Log.d(TAG, "扫码发送失败: " + result);
//                                                        resultContent.setText(result);
//                                                    }

                                                    // 30秒后重新显示MES配置信息
                                                    if (scanHandler != null) {
                                                        scanHandler.postDelayed(new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                if (resultContent != null && !isScanning.get()) {
                                                                    String mesConfig = readMESConfiguration();
                                                                    Log.d(TAG, "30秒后重新显示MES配置信息: " + mesConfig);
                                                                    resultContent.setText(mesConfig);
                                                                }
                                                            }
                                                        }, 30000);
                                                    }
                                                }
                                            }
                                        }, 500);
                                    }
                                });

                            } catch (Exception e) {
                                Log.e(TAG, "MES发送出错: " + e.getMessage());
                                safeUpdateUI(() -> {
                                    if (resultContent != null) {
                                        Log.e(TAG, "显示MES发送错误: " + e.getMessage());
                                        resultContent.setText("MES发送出错: " + e.getMessage());
                                    }
                                });
                            }
                        }
                    }).start();

                } else {
                    Log.e(TAG, "mesCommunication 为 null");
                    if (resultContent != null) {
                        Log.e(TAG, "MES通信未初始化");
                        resultContent.setText("MES通信未初始化");
                    }
                }

            } catch (Exception e) {
                Log.e(TAG, "processCompleted 出错: " + e.getMessage());
            }
        });
    }
}
