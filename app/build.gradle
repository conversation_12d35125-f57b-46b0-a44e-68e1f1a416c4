apply plugin: 'com.android.application'
apply plugin: 'org.sonarqube'

android {
    sonarqube{
        properties{
            property"sonar.projectName","123RFID_mobile_proj"
            property"sonar.projectKey","123RFID_mobile_proj"
            property"sonar.host.url","https://sonar.zebra.com"

            property"sonar.language","java"
            property"sonar.projectBaseDir","c:/myzebra/myprojects/myNGSled/myngsledsdkapp/rfid-integ-main/rfid_android_sdk_app"
            property"sonar.sources","RFID/API/DemoApps/Android/RFIDReaderX_with_API/app/src/main/java"
            property"sonar.login","****************************************"
        }
    }

    signingConfigs {
        config {
            storeFile file('../my-release-key.jks')
            keyAlias "my-alias"
            storePassword System.getenv("KEY_STORE_PWD")
            keyPassword System.getenv("KEY_PWD")
        }
    }
    compileSdkVersion 33

    defaultConfig {
        applicationId 'com.zebra.rfidreaderAPI.demo'
        minSdkVersion 26
        targetSdkVersion 33
        versionCode System.getenv("VERSION_CODE") ? System.getenv("VERSION_CODE").toInteger() : 7
        versionName System.getenv("VERSION_NAME") ? System.getenv("VERSION_NAME").toString() : "2.0.3.7"
        vectorDrawables.useSupportLibrary = true
        signingConfig signingConfigs.config
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
            android.applicationVariants.all { variant ->
                variant.outputs.all {
                    outputFileName = "123_RFID_Mobile-" + defaultConfig.versionName + ".apk"
                }
            }
        }

        debug {
            //signingConfig signingConfigs.config
        }
    }
    compileOptions {
        sourceCompatibility = 1.8
        targetCompatibility = 1.8
    }
    buildToolsVersion '30.0.3'

}
dependencies {

    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.github.chrisbanes:PhotoView:2.0.0'
    implementation 'androidx.navigation:navigation-fragment:2.3.5'
    implementation 'androidx.navigation:navigation-ui:2.3.5'
    implementation "androidx.recyclerview:recyclerview:1.2.1"
    implementation 'com.google.android.gms:play-services-location:18.0.0'
    implementation 'com.google.code.gson:gson:2.8.7'
    implementation 'androidx.camera:camera-view:1.0.0-alpha21'
    implementation "androidx.camera:camera-camera2:1.1.0-alpha08"
    implementation 'androidx.camera:camera-lifecycle:1.0.2'
    implementation 'com.google.android.gms:play-services-mlkit-barcode-scanning:18.0.0'
    implementation 'org.apache.commons:commons-lang3:3.7'
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
}
