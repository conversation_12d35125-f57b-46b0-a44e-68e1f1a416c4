# TCP并发发送问题修复说明

## 问题分析

### 原始问题
在原有的TCP连接实现中存在严重的并发问题：

1. **并发发送冲突**：多个线程同时调用`sendData()`时，可能导致数据混乱
2. **阻塞等待问题**：`sendTCPData()`中的`reader.readLine()`会阻塞，如果此时有新的发送请求会造成问题
3. **资源竞争**：多个线程同时访问同一个Socket的输入输出流
4. **数据丢失风险**：并发情况下可能导致数据发送失败或程序崩溃

### 潜在风险
- 数据发送失败
- 程序崩溃
- 连接异常断开
- 响应数据混乱

## 解决方案

### 1. 发送队列机制
**实现原理**：
- 使用`BlockingQueue<SendDataRequest>`管理所有发送请求
- 所有发送请求先进入队列，由专门的处理器按顺序处理
- 确保同一时间只有一个发送操作在执行

**关键代码**：
```java
// 发送队列和并发控制相关变量
private final BlockingQueue<SendDataRequest> sendQueue = new LinkedBlockingQueue<>();
private final AtomicBoolean isSending = new AtomicBoolean(false);
private ExecutorService sendExecutor;
```

### 2. 发送状态管理
**实现原理**：
- 使用`AtomicBoolean isSending`标记当前发送状态
- 通过`compareAndSet()`确保原子性操作
- 防止并发发送冲突

**关键代码**：
```java
// 设置发送状态
if (!isSending.compareAndSet(false, true)) {
    Log.w(TAG, "正在发送数据，请求被忽略");
    return;
}
```

### 3. 同步锁机制
**实现原理**：
- 在实际发送TCP数据时使用`synchronized (sendLock)`
- 确保Socket读写操作的原子性
- 防止多线程同时操作Socket流

**关键代码**：
```java
synchronized (sendLock) { // 使用同步锁确保发送的原子性
    // TCP发送逻辑
}
```

### 4. 超时控制
**实现原理**：
- 设置Socket读取超时时间
- 避免无限等待服务器响应
- 超时后自动释放资源

**关键代码**：
```java
// 设置Socket超时
tcpSocket.setSoTimeout(READ_TIMEOUT);
```

### 5. 重试机制
**实现原理**：
- 发送失败时自动重试（最多2次）
- 重试请求重新加入队列
- 确保数据发送成功率

**关键代码**：
```java
if (!success && request.retryCount < 2) {
    SendDataRequest retryRequest = new SendDataRequest(request.data, request.retryCount + 1);
    sendQueue.offer(retryRequest);
}
```

## 主要修改内容

### 1. 新增类和变量
```java
// 发送数据请求类
private static class SendDataRequest {
    final String data;
    final long timestamp;
    final int retryCount;
}

// 发送队列和控制变量
private final BlockingQueue<SendDataRequest> sendQueue = new LinkedBlockingQueue<>();
private final AtomicBoolean isSending = new AtomicBoolean(false);
private ExecutorService sendExecutor;
```

### 2. 新增方法
- `startSendQueueProcessor()` - 启动发送队列处理器
- `processSendRequest()` - 处理发送请求
- `performActualSend()` - 执行实际发送
- `sendTCPDataSafe()` - 安全的TCP发送方法
- `reconnectIfNeeded()` - 重连机制

### 3. 修改的方法
- `sendData()` - 改为队列模式，不再直接发送
- `release()` - 添加发送执行器的清理
- 构造函数 - 初始化发送执行器

## 工作流程

### 新的发送流程
```
调用sendData() → 创建SendDataRequest → 加入发送队列 → 
队列处理器取出请求 → 检查发送状态 → 设置发送标志 → 
检查连接状态 → 执行实际发送 → 处理响应 → 清除发送标志 → 
启动连接保持机制
```

### 并发处理
1. **多个发送请求**：按顺序排队处理，避免冲突
2. **发送中的新请求**：加入队列等待，不会被丢弃
3. **连接异常**：自动重连后继续处理队列中的请求
4. **发送失败**：自动重试，确保数据不丢失

## 优势特点

### 1. 线程安全
- 使用队列机制避免并发冲突
- 原子操作确保状态一致性
- 同步锁保护关键资源

### 2. 可靠性提升
- 自动重试机制
- 超时控制避免死锁
- 连接异常自动恢复

### 3. 性能优化
- 队列缓冲减少阻塞
- 专门的发送线程提高效率
- 资源合理管理

### 4. 易于维护
- 清晰的状态管理
- 详细的日志记录
- 模块化设计

## 测试建议

### 1. 并发测试
- 同时发送多个RFID扫描结果
- 快速连续扫描测试
- 多线程压力测试

### 2. 异常测试
- 网络中断时的处理
- 服务器无响应的情况
- 连接超时的恢复

### 3. 性能测试
- 大量数据发送的性能
- 队列满载时的处理
- 内存使用情况监控

## 编译状态
✅ 代码编译成功，无语法错误
✅ 保持了原有的连接保持机制
✅ 向后兼容，不影响现有功能
