# Final verification script
$englishFile = "app/src/main/res/values/strings.xml"
$chineseFile = "app/src/main/res/values-zh/strings.xml"

# Read files
$englishContent = Get-Content $englishFile -Raw
$chineseContent = Get-Content $chineseFile -Raw

# Extract all string and array names from English
$englishStringMatches = Select-String -InputObject $englishContent -Pattern '<string name="([^"]+)"' -AllMatches
$englishArrayMatches = Select-String -InputObject $englishContent -Pattern '<string-array name="([^"]+)"' -AllMatches

$allEnglishItems = @()

# Add all string names
foreach ($match in $englishStringMatches.Matches) {
    $allEnglishItems += "string:$($match.Groups[1].Value)"
}

# Add all array names  
foreach ($match in $englishArrayMatches.Matches) {
    $allEnglishItems += "array:$($match.Groups[1].Value)"
}

Write-Host "Total English items (strings + arrays): $($allEnglishItems.Count)"

# Check each item exists in Chinese
$missingItems = @()
foreach ($item in $allEnglishItems) {
    $type, $name = $item -split ":"
    if ($type -eq "string") {
        $pattern = "<string name=`"$name`""
    } else {
        $pattern = "<string-array name=`"$name`""
    }
    
    if (-not ($chineseContent -match $pattern)) {
        $missingItems += $item
    }
}

if ($missingItems.Count -eq 0) {
    Write-Host "✅ SUCCESS: All English configuration items are present in Chinese version!"
    Write-Host "   - All $($englishStringMatches.Matches.Count) strings are translated"
    Write-Host "   - All $($englishArrayMatches.Matches.Count) arrays are present"
} else {
    Write-Host "❌ MISSING ITEMS: $($missingItems.Count)"
    foreach ($item in $missingItems) {
        Write-Host "   - $item"
    }
}

# Additional checks for critical functionality
$criticalItems = @(
    "language_setting", "language_options", "language_change_title",
    "mes_settings_title", "connection_types", "home_items",
    "options_array", "beeper_volume_array", "session_array"
)

Write-Host "`n=== Critical Items Check ==="
$allCriticalPresent = $true
foreach ($item in $criticalItems) {
    $englishHas = ($englishContent -match "<string.*name=`"$item`"") -or ($englishContent -match "<string-array.*name=`"$item`"")
    $chineseHas = ($chineseContent -match "<string.*name=`"$item`"") -or ($chineseContent -match "<string-array.*name=`"$item`"")
    
    if ($englishHas -and $chineseHas) {
        Write-Host "✅ $item"
    } elseif ($englishHas -and -not $chineseHas) {
        Write-Host "❌ $item (missing in Chinese)"
        $allCriticalPresent = $false
    } else {
        Write-Host "⚠️  $item (not found in English either)"
    }
}

if ($allCriticalPresent) {
    Write-Host "`n🎉 All critical items are properly configured!"
} else {
    Write-Host "`n⚠️  Some critical items need attention!"
}
