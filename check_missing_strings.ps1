# PowerShell script to find missing strings
$englishFile = "app/src/main/res/values/strings.xml"
$chineseFile = "app/src/main/res/values-zh/strings.xml"

# Read files
$englishContent = Get-Content $englishFile -Raw
$chineseContent = Get-Content $chineseFile -Raw

# Extract string names using simpler regex
$englishMatches = Select-String -InputObject $englishContent -Pattern '<string name="([^"]+)"' -AllMatches
$chineseMatches = Select-String -InputObject $chineseContent -Pattern '<string name="([^"]+)"' -AllMatches

$englishStrings = @()
$chineseStrings = @()

foreach ($match in $englishMatches.Matches) {
    $englishStrings += $match.Groups[1].Value
}

foreach ($match in $chineseMatches.Matches) {
    $chineseStrings += $match.Groups[1].Value
}

Write-Host "English strings count: $($englishStrings.Count)"
Write-Host "Chinese strings count: $($chineseStrings.Count)"

# Find missing strings
$missingStrings = $englishStrings | Where-Object { $_ -notin $chineseStrings }
Write-Host "Missing strings count: $($missingStrings.Count)"

if ($missingStrings.Count -gt 0) {
    Write-Host "`n=== Missing Strings ==="
    $missingStrings | Sort-Object | ForEach-Object { Write-Host $_ }
} else {
    Write-Host "`nNo missing strings found!"
}

# Extract array names
$englishArrayMatches = Select-String -InputObject $englishContent -Pattern '<string-array name="([^"]+)"' -AllMatches
$chineseArrayMatches = Select-String -InputObject $chineseContent -Pattern '<string-array name="([^"]+)"' -AllMatches

$englishArrays = @()
$chineseArrays = @()

foreach ($match in $englishArrayMatches.Matches) {
    $englishArrays += $match.Groups[1].Value
}

foreach ($match in $chineseArrayMatches.Matches) {
    $chineseArrays += $match.Groups[1].Value
}

Write-Host "`nEnglish arrays count: $($englishArrays.Count)"
Write-Host "Chinese arrays count: $($chineseArrays.Count)"

# Find missing arrays
$missingArrays = $englishArrays | Where-Object { $_ -notin $chineseArrays }
Write-Host "Missing arrays count: $($missingArrays.Count)"

if ($missingArrays.Count -gt 0) {
    Write-Host "`n=== Missing Arrays ==="
    $missingArrays | Sort-Object | ForEach-Object { Write-Host $_ }
} else {
    Write-Host "`nNo missing arrays found!"
}
