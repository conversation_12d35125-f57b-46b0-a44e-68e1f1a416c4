# Detailed comparison script
$englishFile = "app/src/main/res/values/strings.xml"
$chineseFile = "app/src/main/res/values-zh/strings.xml"

# Read files
$englishContent = Get-Content $englishFile -Raw
$chineseContent = Get-Content $chineseFile -Raw

# Check specific important strings that might be missing
$importantStrings = @(
    "language_setting",
    "language_change_title", 
    "language_change_message",
    "restart",
    "mes_settings_title",
    "msg_server_address",
    "connection_failed",
    "unknown_connection_type",
    "disconnectrfid",
    "mes_communication_not_initialized"
)

Write-Host "=== Checking Important Strings ==="
foreach ($stringName in $importantStrings) {
    $englishHas = $englishContent -match "<string name=`"$stringName`""
    $chineseHas = $chineseContent -match "<string name=`"$stringName`""
    
    $status = if ($englishHas -and $chineseHas) { "✓ OK" } 
              elseif ($englishHas -and -not $chineseHas) { "✗ MISSING in Chinese" }
              elseif (-not $englishHas -and $chineseHas) { "? Extra in Chinese" }
              else { "? Not in either" }
    
    Write-Host "$stringName : $status"
}

# Check file line counts
$englishLines = (Get-Content $englishFile).Count
$chineseLines = (Get-Content $chineseFile).Count

Write-Host "`n=== File Statistics ==="
Write-Host "English file lines: $englishLines"
Write-Host "Chinese file lines: $chineseLines"
Write-Host "Difference: $($englishLines - $chineseLines) lines"

# Check for any obvious issues
Write-Host "`n=== File Integrity Check ==="
$englishValid = $englishContent -match "</resources>"
$chineseValid = $chineseContent -match "</resources>"

Write-Host "English file ends properly: $englishValid"
Write-Host "Chinese file ends properly: $chineseValid"

# Sample some random strings to verify they exist in both
$sampleStrings = @("app_name", "hello_world", "action_settings", "rapid_read")
Write-Host "`n=== Sample String Check ==="
foreach ($sample in $sampleStrings) {
    $englishHas = $englishContent -match "<string name=`"$sample`""
    $chineseHas = $chineseContent -match "<string name=`"$sample`""
    $status = if ($englishHas -and $chineseHas) { "✓" } else { "✗" }
    Write-Host "$sample : $status"
}
