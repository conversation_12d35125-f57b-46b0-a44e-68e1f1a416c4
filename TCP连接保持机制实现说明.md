# TCP连接保持机制实现说明

## 功能概述

实现了TCP连接保持机制，满足以下需求：
1. 在发送数据后3分钟内没有再次发送，则断开连接
2. 如3分钟内有再次发送数据，则重置为3分钟等待时间
3. 当前活动页面不是RFID页面时，断开与服务的连接

## 修改的文件

### 1. MESCommunication.java
**文件路径**: `app/src/main/java/com/zebra/demo/mes_settings/MESCommunication.java`

**主要修改内容**:

#### 添加的变量
```java
// 连接保持机制相关变量
private Timer connectionKeepAliveTimer;
private final int CONNECTION_KEEP_ALIVE_TIMEOUT = 3 * 60 * 1000; // 3分钟
private volatile boolean keepConnectionAlive = false;
private volatile long lastDataSentTime = 0;
```

#### 添加的方法
1. **startConnectionKeepAliveTimer()** - 启动连接保持定时器
2. **stopConnectionKeepAliveTimer()** - 停止连接保持定时器
3. **resetConnectionKeepAliveTimer()** - 重置连接保持定时器
4. **isCurrentPageRFID()** - 检查当前页面是否是RFID页面
5. **notifyPageChanged()** - 外部调用方法，通知页面切换

#### 修改的方法
1. **sendData()** - 修改发送完成后的逻辑，TCP连接启用保持机制而不是立即断开
2. **sendTCPData()** - 添加重置定时器的调用
3. **disconnect()** - 添加停止连接保持定时器
4. **release()** - 添加停止连接保持定时器

### 2. RapidReadFragment.java
**文件路径**: `app/src/main/java/com/zebra/demo/rfidreader/rapidread/RapidReadFragment.java`

**主要修改内容**:

#### 修改的方法
1. **onPause()** - 添加页面切换时通知MES通信断开连接的逻辑

```java
// 通知MES通信断开连接（页面切换时）
if (mesCommunication != null) {
    Log.d(TAG, "页面切换，通知MES断开连接");
    mesCommunication.notifyPageChanged();
}
```

## 实现逻辑

### 连接保持流程
1. **数据发送时**：
   - TCP连接发送数据完成后，启动3分钟定时器
   - 非TCP连接仍然立即断开（保持原有逻辑）

2. **定时器机制**：
   - 每次发送数据时重置定时器
   - 定时器到期时检查是否超过3分钟没有发送数据
   - 如果超时，自动断开连接

3. **页面切换检测**：
   - RapidReadFragment的onPause()方法被调用时，主动断开连接
   - 确保离开RFID页面时立即断开连接

### 关键特性
1. **只对TCP连接启用**：保持机制只对TCP连接生效，其他连接类型保持原有的立即断开逻辑
2. **线程安全**：使用volatile关键字和Timer确保多线程环境下的安全性
3. **资源管理**：在disconnect()和release()方法中确保定时器被正确停止
4. **日志记录**：添加详细的日志记录，便于调试和监控

## 使用说明

### 正常工作流程
1. 扫描RFID或条码
2. 发送数据到MES服务器
3. 连接保持3分钟
4. 如果3分钟内再次扫描，重置定时器
5. 如果3分钟内没有操作或切换页面，自动断开连接

### 注意事项
1. 该机制只对TCP连接生效
2. 页面切换时会立即断开连接，无论是否在3分钟内
3. 应用退出或Fragment销毁时会自动清理资源

## 测试建议

1. **基本功能测试**：
   - 扫描数据后验证连接保持3分钟
   - 3分钟内再次扫描验证定时器重置
   - 3分钟后验证自动断开

2. **页面切换测试**：
   - 从RFID页面切换到其他页面验证立即断开
   - 返回RFID页面后验证可以正常重新连接

3. **异常情况测试**：
   - 网络异常时的处理
   - 应用后台运行时的行为
   - 内存不足时的资源清理

## 编译状态
✅ 代码编译成功，无语法错误
